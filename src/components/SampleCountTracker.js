import React, { useState, useEffect } from 'react';
import { fetchSampleCounts, getSampleProgress } from '../services/sampleCountService';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
  CircularProgress
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PersonIcon from '@mui/icons-material/Person';
import GroupIcon from '@mui/icons-material/Group';
import EqualizerIcon from '@mui/icons-material/Equalizer';

const SampleCountTracker = ({ recordings, phrases, demographics }) => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [sampleCounts, setSampleCounts] = useState({});

  // Demographics categories for tracking
  const demographicCategories = {
    gender: ['male', 'female', 'nonbinary'],
    ageGroup: ['18to39', '40to64', '65plus'],
    ethnicity: ['asian', 'caucasian', 'aboriginal', 'african', 'latinx', 'middle_eastern', 'pacific_islander', 'indigenous']
  };

  const demographicLabels = {
    gender: {
      male: 'Male',
      female: 'Female',
      nonbinary: 'Non-binary'
    },
    ageGroup: {
      '18to39': '18-39 years',
      '40to64': '40-64 years',
      '65plus': '65+ years'
    },
    ethnicity: {
      asian: 'Asian',
      caucasian: 'Caucasian',
      aboriginal: 'Aboriginal',
      african: 'African',
      latinx: 'Latinx',
      middle_eastern: 'Middle Eastern',
      pacific_islander: 'Pacific Islander',
      indigenous: 'Indigenous'
    }
  };

  // Fetch sample counts from the server
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [error, setError] = useState(null);

  // Fetch sample counts from the API
  useEffect(() => {
    const loadSampleCounts = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const data = await fetchSampleCounts();
        if (data.success) {
          setSampleCounts(data.counts);
          setLastUpdated(data.lastUpdated);
        } else {
          setError(data.error || 'Failed to load sample counts');
          // Initialize with empty data
          setSampleCounts({
            byGender: { male: 0, female: 0, nonbinary: 0 },
            byAgeGroup: { '18to39': 0, '40to64': 0, '65plus': 0 },
            byEthnicity: {},
            byPhrase: {},
            total: 0
          });
        }
      } catch (err) {
        console.error('Error loading sample counts:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    // Load sample counts on component mount
    loadSampleCounts();

    // Set up auto-refresh every 30 seconds
    const refreshInterval = setInterval(() => {
      console.log('Auto-refreshing sample counts...');
      loadSampleCounts();
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, []); // Only run on component mount

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const getCompletionColor = (count) => {
    if (count === 0) return '#f44336';
    if (count < 3) return '#ff9800';
    return '#4caf50';
  };

  const renderOverviewStats = () => (
    <Grid container spacing={3} sx={{ mb: 3 }}>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <EqualizerIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Total Recordings</Typography>
            </Box>
            <Typography variant="h4" color="primary">
              {sampleCounts.total || 0}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <GroupIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Phrases Recorded</Typography>
            </Box>
            <Typography variant="h4" color="primary">
              {Object.values(recordings).filter(count => count > 0).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              of {Object.keys(sampleCounts.byPhrase || {}).length} total phrases
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PersonIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Complete Phrases</Typography>
            </Box>
            <Typography variant="h4" color="primary">
              {Object.values(recordings).filter(count => count >= 3).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              phrases with 3+ recordings
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6">Completion Rate</Typography>
            </Box>
            <Typography variant="h4" color="primary">
              {Object.keys(sampleCounts.byPhrase || {}).length > 0 
                ? Math.round((Object.values(recordings).filter(count => count >= 3).length / Object.keys(sampleCounts.byPhrase).length) * 100)
                : 0}%
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={Object.keys(sampleCounts.byPhrase || {}).length > 0 
                ? (Object.values(recordings).filter(count => count >= 3).length / Object.keys(sampleCounts.byPhrase).length) * 100
                : 0}
              sx={{ mt: 1 }}
            />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderDemographicBreakdown = (category) => (
    <TableContainer component={Paper} sx={{ mt: 2 }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>{category.charAt(0).toUpperCase() + category.slice(1)}</TableCell>
            <TableCell align="right">Sample Count</TableCell>
            <TableCell align="right">Percentage</TableCell>
            <TableCell align="center">Status</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {demographicCategories[category].map(value => {
            const count = sampleCounts[`by${category.charAt(0).toUpperCase() + category.slice(1)}`]?.[value] || 0;
            const percentage = sampleCounts.total > 0 ? Math.round((count / sampleCounts.total) * 100) : 0;
            return (
              <TableRow key={value}>
                <TableCell component="th" scope="row">
                  {demographicLabels[category][value]}
                </TableCell>
                <TableCell align="right">
                  <Badge badgeContent={count} color="primary">
                    <Typography variant="body2">{count}</Typography>
                  </Badge>
                </TableCell>
                <TableCell align="right">{percentage}%</TableCell>
                <TableCell align="center">
                  <Chip
                    label={count === 0 ? 'No Data' : count < 5 ? 'Low' : count < 15 ? 'Good' : 'Excellent'}
                    color={count === 0 ? 'error' : count < 5 ? 'warning' : 'success'}
                    size="small"
                  />
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderPhraseBreakdown = () => (
    <Box sx={{ mt: 2 }}>
      {Object.entries(phrases).map(([category, categoryPhrases]) => (
        <Accordion key={category} sx={{ mb: 1 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">{category}</Typography>
            <Chip 
              label={`${categoryPhrases.filter(phrase => recordings[`${category}:${phrase}`] >= 3).length}/${categoryPhrases.length} complete`}
              color={categoryPhrases.filter(phrase => recordings[`${category}:${phrase}`] >= 3).length === categoryPhrases.length ? 'success' : 'primary'}
              size="small"
              sx={{ ml: 2 }}
            />
          </AccordionSummary>
          <AccordionDetails>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Phrase</TableCell>
                    <TableCell align="center">Recordings</TableCell>
                    <TableCell align="center">Progress</TableCell>
                    <TableCell align="center">Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {categoryPhrases.map(phrase => {
                    const phraseKey = `${category}:${phrase}`;
                    const count = recordings[phraseKey] || 0;
                    return (
                      <TableRow key={phrase}>
                        <TableCell component="th" scope="row">
                          {phrase}
                        </TableCell>
                        <TableCell align="center">
                          <Typography variant="body2" color={getCompletionColor(count)}>
                            {count}/3
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <LinearProgress
                            variant="determinate"
                            value={(count / 3) * 100}
                            sx={{ width: 60, height: 8 }}
                            color={count >= 3 ? 'success' : 'primary'}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Chip
                            label={count === 0 ? 'Not Started' : count >= 3 ? 'Complete' : 'In Progress'}
                            color={count === 0 ? 'default' : count >= 3 ? 'success' : 'primary'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );

  return (
    <Box sx={{ width: '100%', p: 2 }}>
      <Paper elevation={3} sx={{ p: 2, borderRadius: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Sample Count Tracker
            {isLoading && (
              <CircularProgress size={20} sx={{ ml: 2, verticalAlign: 'middle' }} />
            )}
          </Typography>
          {lastUpdated && (
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </Typography>
          )}
          {error && (
            <Typography variant="caption" color="error" sx={{ display: 'block', mb: 1 }}>
              Error: {error}
            </Typography>
          )}
          <Tabs value={selectedTab} onChange={handleTabChange}>
            <Tab label="By Phrase" />
            <Tab label="By Demographics" />
          </Tabs>
        </Box>
        
        <Box sx={{ p: 2 }}>
          {selectedTab === 0 && renderPhraseBreakdown()}
          {selectedTab === 1 && renderDemographicBreakdown('gender')}
        </Box>
      </Paper>
    </Box>
  );
};

export default SampleCountTracker;
